<template>
  <view class="login-container">
    <!-- 背景装饰 -->
    <view class="bg-decoration">
      <view class="paw-print paw-1">🐾</view>
      <view class="paw-print paw-2">🐾</view>
      <view class="paw-print paw-3">🐾</view>
      <view class="heart heart-1">💕</view>
      <view class="heart heart-2">💕</view>
    </view>

    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- Logo区域 -->
      <view class="logo-section">
        <view class="logo-icon">🐱</view>
        <text class="app-name">萌宠之家</text>
        <text class="app-slogan">与毛孩子的温馨时光</text>
      </view>

      <!-- 登录表单 -->
      <view class="login-form">
        <!-- 登录方式切换 -->
        <view class="login-tabs">
          <view
            class="tab-item"
            :class="{ active: loginType === 'phone' }"
            @click="switchLoginType('phone')"
          >
            <text class="tab-text">手机登录</text>
          </view>
          <view
            class="tab-item"
            :class="{ active: loginType === 'password' }"
            @click="switchLoginType('password')"
          >
            <text class="tab-text">密码登录</text>
          </view>
        </view>

        <!-- 手机号登录 -->
        <view v-if="loginType === 'phone'" class="form-content">
          <view class="input-group">
            <view class="input-wrapper">
              <text class="input-icon">📱</text>
              <input
                class="input-field"
                type="number"
                placeholder="请输入手机号"
                v-model="phoneNumber"
                maxlength="11"
              />
            </view>
          </view>

          <view class="input-group">
            <view class="input-wrapper">
              <text class="input-icon">🔐</text>
              <input
                class="input-field verification-input"
                type="number"
                placeholder="请输入验证码"
                v-model="verificationCode"
                maxlength="6"
              />
              <view
                class="send-code-btn"
                :class="{ disabled: !canSendCode }"
                @click="sendVerificationCode"
              >
                <text class="send-code-text">{{ codeButtonText }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 密码登录 -->
        <view v-if="loginType === 'password'" class="form-content">
          <view class="input-group">
            <view class="input-wrapper">
              <text class="input-icon">👤</text>
              <input
                class="input-field"
                type="text"
                placeholder="请输入账号/手机号"
                v-model="username"
              />
            </view>
          </view>

          <view class="input-group">
            <view class="input-wrapper">
              <text class="input-icon">🔒</text>
              <input
                class="input-field"
                :type="showPassword ? 'text' : 'password'"
                placeholder="请输入密码"
                v-model="password"
              />
              <view class="eye-icon" @click="togglePassword">
                <text>{{ showPassword ? '👁️' : '👁️‍🗨️' }}</text>
              </view>
            </view>
          </view>

          <view class="forgot-password">
            <text class="forgot-text" @click="forgotPassword">忘记密码？</text>
          </view>
        </view>

        <!-- 登录按钮 -->
        <view class="login-button-wrapper">
          <view
            class="login-button"
            :class="{ disabled: !canLogin }"
            @click="handleLogin"
          >
            <text class="login-text">{{ loginButtonText }}</text>
          </view>
        </view>

        <!-- 第三方登录 -->
        <view class="third-party-login">
          <view class="divider">
            <view class="divider-line"></view>
            <text class="divider-text">其他登录方式</text>
            <view class="divider-line"></view>
          </view>

          <view class="social-login-buttons">
            <view class="social-btn wechat-btn" @click="wechatLogin">
              <text class="social-icon">💬</text>
              <text class="social-text">微信登录</text>
            </view>
          </view>
        </view>

        <!-- 注册提示 -->
        <view class="register-hint">
          <text class="hint-text">还没有账号？</text>
          <text class="register-link" @click="goToRegister">立即注册</text>
        </view>
      </view>
    </view>

    <!-- 底部装饰 -->
    <view class="bottom-decoration">
      <view class="pet-silhouette">🐕</view>
      <view class="pet-silhouette">🐈</view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';

// 登录类型
const loginType = ref('phone');

// 表单数据
const phoneNumber = ref('');
const verificationCode = ref('');
const username = ref('');
const password = ref('');
const showPassword = ref(false);

// 验证码相关
const countdown = ref(0);
const isLoading = ref(false);

// 计算属性
const canSendCode = computed(() => {
  return phoneNumber.value.length === 11 && countdown.value === 0;
});

const codeButtonText = computed(() => {
  if (countdown.value > 0) {
    return `${countdown.value}s后重发`;
  }
  return '发送验证码';
});

const canLogin = computed(() => {
  if (loginType.value === 'phone') {
    return phoneNumber.value.length === 11 && verificationCode.value.length === 6;
  } else {
    return username.value.length > 0 && password.value.length > 0;
  }
});

const loginButtonText = computed(() => {
  return isLoading.value ? '登录中...' : '登录';
});

// 方法
const switchLoginType = (type) => {
  loginType.value = type;
  // 清空表单数据
  phoneNumber.value = '';
  verificationCode.value = '';
  username.value = '';
  password.value = '';
};

const sendVerificationCode = async () => {
  if (!canSendCode.value) return;

  try {
    // 这里调用发送验证码的API
    console.log('发送验证码到:', phoneNumber.value);

    // 开始倒计时
    countdown.value = 60;
    const timer = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        clearInterval(timer);
      }
    }, 1000);

    uni.showToast({
      title: '验证码已发送',
      icon: 'success'
    });
  } catch (error) {
    uni.showToast({
      title: '发送失败，请重试',
      icon: 'error'
    });
  }
};

const togglePassword = () => {
  showPassword.value = !showPassword.value;
};

const handleLogin = async () => {
  if (!canLogin.value || isLoading.value) return;

  isLoading.value = true;

  try {
    if (loginType.value === 'phone') {
      // 手机号验证码登录
      console.log('手机号登录:', { phone: phoneNumber.value, code: verificationCode.value });
    } else {
      // 账号密码登录
      console.log('密码登录:', { username: username.value, password: password.value });
    }

    // 模拟登录成功
    uni.showToast({
      title: '登录成功',
      icon: 'success'
    });

    // 跳转到首页
    setTimeout(() => {
      uni.switchTab({
        url: '/pages/index/index'
      });
    }, 1500);

  } catch (error) {
    uni.showToast({
      title: '登录失败，请重试',
      icon: 'error'
    });
  } finally {
    isLoading.value = false;
  }
};

const wechatLogin = async () => {
  try {
    // 微信登录逻辑
    console.log('微信登录');
    uni.showToast({
      title: '微信登录功能开发中',
      icon: 'none'
    });
  } catch (error) {
    uni.showToast({
      title: '微信登录失败',
      icon: 'error'
    });
  }
};

const forgotPassword = () => {
  uni.navigateTo({
    url: '/pages/forgot-password/forgot-password'
  });
};

const goToRegister = () => {
  uni.navigateTo({
    url: '/pages/register/register'
  });
};
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FFF8E7 0%, #FFE4B5 50%, #FFDAB9 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40rpx 60rpx;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.paw-print {
  position: absolute;
  font-size: 60rpx;
  opacity: 0.1;
  animation: float 6s ease-in-out infinite;
}

.paw-1 {
  top: 15%;
  left: 10%;
  animation-delay: 0s;
}

.paw-2 {
  top: 25%;
  right: 15%;
  animation-delay: 2s;
}

.paw-3 {
  bottom: 30%;
  left: 20%;
  animation-delay: 4s;
}

.heart {
  position: absolute;
  font-size: 40rpx;
  opacity: 0.15;
  animation: pulse 4s ease-in-out infinite;
}

.heart-1 {
  top: 40%;
  right: 10%;
  animation-delay: 1s;
}

.heart-2 {
  bottom: 20%;
  right: 25%;
  animation-delay: 3s;
}

/* 主要内容区域 */
.main-content {
  width: 100%;
  max-width: 600rpx;
  z-index: 2;
  position: relative;
}

/* Logo区域 */
.logo-section {
  text-align: center;
  margin-bottom: 80rpx;
}

.logo-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
  animation: bounce 2s ease-in-out infinite;
}

.app-name {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #FF8C42;
  margin-bottom: 10rpx;
  text-shadow: 2rpx 2rpx 4rpx rgba(255, 140, 66, 0.3);
}

.app-slogan {
  display: block;
  font-size: 28rpx;
  color: #D2691E;
  opacity: 0.8;
}

/* 登录表单 */
.login-form {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 40rpx;
  padding: 60rpx 50rpx;
  box-shadow: 0 20rpx 40rpx rgba(255, 140, 66, 0.15);
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 140, 66, 0.2);
}

/* 登录方式切换 */
.login-tabs {
  display: flex;
  background: #FFF8E7;
  border-radius: 30rpx;
  padding: 8rpx;
  margin-bottom: 50rpx;
  border: 2rpx solid #FFE4B5;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  border-radius: 24rpx;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: linear-gradient(135deg, #FF8C42, #FFA500);
  box-shadow: 0 8rpx 16rpx rgba(255, 140, 66, 0.3);
}

.tab-text {
  font-size: 30rpx;
  font-weight: 500;
  color: #8B4513;
  transition: color 0.3s ease;
}

.tab-item.active .tab-text {
  color: white;
  font-weight: bold;
}

/* 表单内容 */
.form-content {
  margin-bottom: 40rpx;
}

.input-group {
  margin-bottom: 30rpx;
}

.input-wrapper {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 25rpx;
  padding: 0 30rpx;
  border: 3rpx solid #FFE4B5;
  transition: all 0.3s ease;
  position: relative;
}

.input-wrapper:focus-within {
  border-color: #FF8C42;
  box-shadow: 0 0 0 6rpx rgba(255, 140, 66, 0.1);
}

.input-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
  opacity: 0.7;
}

.input-field {
  flex: 1;
  height: 90rpx;
  font-size: 32rpx;
  color: #333;
  border: none;
  outline: none;
  background: transparent;
}

.verification-input {
  padding-right: 160rpx;
}

.send-code-btn {
  position: absolute;
  right: 20rpx;
  background: linear-gradient(135deg, #FF8C42, #FFA500);
  color: white;
  padding: 16rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.send-code-btn.disabled {
  background: #DDD;
  color: #999;
}

.send-code-text {
  font-size: 24rpx;
  font-weight: 500;
}

.eye-icon {
  position: absolute;
  right: 30rpx;
  font-size: 32rpx;
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.eye-icon:active {
  opacity: 1;
}

/* 忘记密码 */
.forgot-password {
  text-align: right;
  margin-top: 20rpx;
}

.forgot-text {
  font-size: 26rpx;
  color: #FF8C42;
  text-decoration: underline;
}

/* 登录按钮 */
.login-button-wrapper {
  margin: 50rpx 0 40rpx 0;
}

.login-button {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #FF8C42, #FFA500);
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 15rpx 30rpx rgba(255, 140, 66, 0.4);
  transition: all 0.3s ease;
}

.login-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 10rpx 20rpx rgba(255, 140, 66, 0.3);
}

.login-button.disabled {
  background: #DDD;
  box-shadow: none;
}

.login-text {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  text-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 第三方登录 */
.third-party-login {
  margin: 40rpx 0;
}

.divider {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.divider-line {
  flex: 1;
  height: 2rpx;
  background: #FFE4B5;
}

.divider-text {
  margin: 0 20rpx;
  font-size: 24rpx;
  color: #D2691E;
  opacity: 0.8;
}

.social-login-buttons {
  display: flex;
  justify-content: center;
}

.social-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 40rpx;
  border-radius: 25rpx;
  transition: all 0.3s ease;
  min-width: 200rpx;
}

.wechat-btn {
  background: linear-gradient(135deg, #07C160, #00D100);
  box-shadow: 0 10rpx 20rpx rgba(7, 193, 96, 0.3);
}

.wechat-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 5rpx 10rpx rgba(7, 193, 96, 0.2);
}

.social-icon {
  font-size: 32rpx;
  margin-right: 10rpx;
}

.social-text {
  font-size: 28rpx;
  color: white;
  font-weight: 500;
}

/* 注册提示 */
.register-hint {
  text-align: center;
  margin-top: 30rpx;
}

.hint-text {
  font-size: 28rpx;
  color: #8B4513;
  margin-right: 10rpx;
}

.register-link {
  font-size: 28rpx;
  color: #FF8C42;
  font-weight: bold;
  text-decoration: underline;
}

/* 底部装饰 */
.bottom-decoration {
  position: absolute;
  bottom: 50rpx;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  z-index: 1;
  pointer-events: none;
}

.pet-silhouette {
  font-size: 80rpx;
  opacity: 0.1;
  animation: sway 4s ease-in-out infinite;
}

.pet-silhouette:nth-child(2) {
  animation-delay: 2s;
}

/* 动画效果 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20rpx) rotate(5deg);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.15;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.25;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10rpx);
  }
  60% {
    transform: translateY(-5rpx);
  }
}

@keyframes sway {
  0%, 100% {
    transform: rotate(-2deg);
  }
  50% {
    transform: rotate(2deg);
  }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .login-container {
    padding: 30rpx 40rpx;
  }

  .login-form {
    padding: 50rpx 40rpx;
  }

  .logo-icon {
    font-size: 100rpx;
  }

  .app-name {
    font-size: 42rpx;
  }
}
</style>